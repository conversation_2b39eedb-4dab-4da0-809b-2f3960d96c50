"use client"
import { Col, Container, Row } from "react-bootstrap";
import CommonButton from "@/Components/UI/CommonButton";
import Link from "next/link";
import { CheckIcon, RedCrossIcon } from "@/assets/svgIcons/SvgIcon";
import { useRouter } from "next/navigation";
import Switch from "@/Components/UI/Switch";
import FaqCard from "@/Components/common/Home/FaqCard";
import HomeLayout from "@/Layouts/HomeLayout";
import "@/css/Home/Pricing.scss";
import { useEffect, useState } from "react";
import MetaHead from "@/Seo/Meta/MetaHead";
import { usePathname, useSearchParams } from "next/navigation";
import Cookies from "js-cookie";
import Loader from "@/Components/common/Loader";
import { loadStripe } from '@stripe/stripe-js';


const PricingClient = () => {
    const [loginToken, setLoginToken] = useState(null);
    const router = useRouter();
    const [isMonthly, setIsMonthly] = useState(false);
    const [isFree, setisFree] = useState(false);
    const [currentPlan, setCurrentPlan] = useState("Free");
    const [currentBilling, setCurrentBilling] = useState("yearly");
    const [isLoggedIn, setIsLoggedIn] = useState(false);
    const [plans, setPlans] = useState([]);
    const [billingPlans, setBillingPlans] = useState([]);
    const [isLoading, setIsLoading] = useState(false);

    const pathname = usePathname();
    const searchParams = useSearchParams();


    useEffect(() => {
        if (typeof window !== "undefined") {
            const token = Cookies.get("authToken");
            const storedPlan = localStorage.getItem("plan");
            const storedBilling = localStorage.getItem("plan_billing_type");

            setLoginToken(token);
            setIsLoggedIn(!!token);

            if (storedPlan) {
                setCurrentPlan(storedPlan);
                setisFree(storedPlan === "Free");
            }

            if (storedBilling) {
                setCurrentBilling(storedBilling);
            }

            setIsLoading(false);
        }
    }, []);


    const handleSwitchplan = () => {
        const newIsMonthly = !isMonthly;
        setIsMonthly(newIsMonthly);
        const filteredPlans = plans.filter(plan => plan.billing_type === (newIsMonthly ? "monthly" : "yearly"));
        setBillingPlans(filteredPlans);
    };


    useEffect(() => {

        const hasExtraPath = pathname !== "/pricing";
        const hasQueryParams = searchParams.toString().length > 0;

        if (hasExtraPath || hasQueryParams) {
            setisFree(true);
        }
    }, []);

    useEffect(() => {
        const tokens = Cookies.get("authToken");
        setLoginToken(tokens);
    }, []);


    useEffect(() => {
        const fetchPricingPlans = async () => {
            try {
                const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/stripe/plans`);
                const { data } = await response.json();
                setPlans(data);
                const filtered = data.filter(plan => plan.billing_type === (isMonthly ? "monthly" : "yearly"));
                setBillingPlans(filtered);
            } catch (error) {
                console.error("Error fetching plans:", error);
            }
        };
        fetchPricingPlans();
    }, [isMonthly]);



    // stripe api call to send the id and get session-id then moved to checkout stripe url

    const handleSubscribe = async (planId) => {
        if (!loginToken) {
            router.push("/signup");
            return;
        }

        try {
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/subscribe`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${loginToken}`,
                },
                body: JSON.stringify({ plan_id: planId }),
            });

            const data = await response.json();
            debugger;
            if (data?.session_id) {
                const stripe = await loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLIC_KEY);
                await stripe.redirectToCheckout({ sessionId: data.session_id });
            }
            else {
                throw new Error("No session_id in response");
            }

        } catch (error) {
            console.error("Checkout error:", error);
            alert("Something went wrong while subscribing. Please try again.");
        }
    };




    // upper APis when I am doing payemnt moved to https://dev.tradereply.com/pricing?session_id=cs_test_a1VkvMV5Dkqzue9WVT3Rp9LHGhsy8BaefeKOU5Gdv6I8dA1VkNSvRTz79V
    // now I am getting this session id 

    useEffect(() => {
        const sessionId = searchParams.get("session_id");
        console.log(sessionId);
        console.log(loginToken);

        if (sessionId && loginToken) {
            const confirmSubscription = async () => {
                try {
                    const response = await fetch(
                        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/confirm/subscribe`,
                        {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                                Authorization: `Bearer ${loginToken}`,
                            },
                            body: JSON.stringify({ session_id: sessionId }),
                        }
                    );

                    if (!response.ok) {
                        throw new Error("Subscription confirmation failed");
                    }
                    const data = await response.json();
                    console.log("Subscription confirmed:", data);
                    debugger;
                    const subscription = data?.data; // assuming your API wraps response in `data`

                    if (subscription && subscription.plan_id) {
                        // Optionally map plan_id to human-readable plan name
                        const planMap = {
                            15: { name: "Essential", billing: "monthly" },
                            16: { name: "Essential", billing: "yearly" },
                            17: { name: "Plus", billing: "monthly" },
                            18: { name: "Plus", billing: "yearly" },
                            19: { name: "Premium", billing: "monthly" },
                            20: { name: "Premium", billing: "yearly" },
                        };

                        const newPlan = planMap[subscription.plan_id] || { name: "Free", billing: "monthly" };

                        setCurrentPlan(newPlan.name);
                        setCurrentBilling(newPlan.billing);

                        if (typeof window !== "undefined") {
                            localStorage.setItem("plan", newPlan.name);
                            localStorage.setItem("plan_billing_type", newPlan.billing);
                            localStorage.setItem("subscriptionData", JSON.stringify(subscription));
                        }

                        alert("Payment successful! Your subscription is now active.");
                        const url = new URL(window.location.href);
                        url.searchParams.delete("session_id");
                        window.history.replaceState({}, "", url.toString());
                    }
                } catch (error) {
                    console.error("Error confirming subscription:", error);
                    alert("Something went wrong while confirming your payment.");
                }
            };

            confirmSubscription();
        }
    }, [searchParams, loginToken]);



    const metaArray = {
        title: "TradeReply Pricing Plans | Choose Your Trading Tools",
        description: "Discover flexible pricing plans at TradeReply.com. Choose the perfect plan to access advanced trading tools, real-time analytics, and strategy optimization.",
        canonical_link: "https://www.tradereply.com/pricing",
        og_site_name: "TradeReply",
        og_title: "TradeReply Pricing Plans | Choose Your Trading Tools",
        og_description: "Discover flexible pricing plans at TradeReply.com. Choose the perfect plan to access advanced trading tools, real-time analytics, and strategy optimization.",
        twitter_title: "TradeReply Pricing Plans | Choose Your Trading Tools",
        twitter_description: "Discover flexible pricing plans at TradeReply.com. Choose the perfect plan to access advanced trading tools, real-time analytics, and strategy optimization.",
    };
    return (
        <HomeLayout>
            <MetaHead props={metaArray} />
            <div className="pricing">
                <section className="pricing_banner">
                    <Container>

                        <Row className="gx-xl-5 align-items-center justify-content-center">
                            <Col md={isFree ? 12 : 7} xs={12} xl={isFree ? 12 : 8}>
                                <div className="pricing_banner_content text-center">
                                    <h1>
                                        {isLoggedIn
                                            ? currentPlan === "Free"
                                                ? "Try any of our plans, free for 30 days"
                                                : "Manage your plan or explore additional features"
                                            : "Join Free, Upgrade Anytime"}
                                    </h1>

                                    <p>
                                        {currentPlan === "Free" && !isLoggedIn
                                            ? "Enjoy powerful insights with our free analytics suite. Upgrade anytime for additional premium features."
                                            : currentPlan === "Essential"
                                                ? "You’re currently on the Essential plan. Unlock more advanced tools by upgrading to Plus or Premium."
                                                : currentPlan === "Plus"
                                                    ? "You’re currently on the Plus plan. Want even deeper insights and automation? Upgrade to Premium anytime."
                                                    : currentPlan === "Premium"
                                                        ? "You’re on our most advanced plan — Premium. Looking to downgrade or adjust your billing preferences?"
                                                        : ""}
                                    </p>
                                </div>
                            </Col>

                            {!isFree && (
                                <Col md={5} xs={12} xl={4}>
                                    <div className="pricing_banner_forever">
                                        <h3>$0 forever</h3>
                                        <div>
                                            <CommonButton
                                                onClick={() => {
                                                    sessionStorage.setItem("pricing", "free");
                                                    sessionStorage.setItem("trial", "false");
                                                    router.push("/signup");
                                                }}
                                                title="Join Free"
                                                className="gradient-btn my-3 my-md-4"
                                            />
                                        </div>
                                        <h4>No Credit Card Required</h4>
                                    </div>
                                </Col>
                            )}
                        </Row>


                    </Container>
                </section>



                <section className="pricing_table">
                    <Container>
                        <div className="pricing_table_switch d-flex align-items-center justify-content-center">
                            <p>Monthly</p>
                            <Switch
                                checked={Boolean(!isMonthly)}
                                onChange={handleSwitchplan}
                            />
                            <p>Annually</p>
                        </div>
                        <Row className="gx-0 gy-4">
                            {billingPlans?.map((item, index) => {
                                console.log("itemssss", item);
                                const planName = item?.title;
                                const planOrder = ["Free", "Essential", "Plus", "Premium"];
                                const currentIndex = planOrder.indexOf(currentPlan);
                                const thisIndex = planOrder.indexOf(planName);

                                let buttonTitle = "";
                                let buttonClass = "";
                                let showButton = true;

                                if (planName === currentPlan) {
                                    buttonTitle = "Current Plan";
                                    buttonClass = "gray-btn";
                                } else if (currentPlan === "Free") {
                                    buttonTitle = "Try Free for 30 Days";
                                    buttonClass = "green-btn free_for";
                                } else if (thisIndex > currentIndex) {
                                    buttonTitle = `Upgrade to ${planName}`;
                                    buttonClass = "green-btn";
                                } else if (thisIndex < currentIndex) {
                                    buttonTitle = `Downgrade to ${planName}`;
                                    buttonClass = "yellow-btn";
                                }


                                return (
                                    <Col lg={4} xs={12} key={index} className="pricing_table_col d-flex">
                                        <div className="pricing_table_box w-100">
                                            <div className="pricing_table_box_heading">
                                                <h3>{item?.title}</h3>
                                                <h2>
                                                    ${item?.price}
                                                    <span> /${isMonthly ? 'month' : 'year'} </span>
                                                </h2>
                                                {!isMonthly && (
                                                    <>
                                                        <p>{item?.billed_description || ''}</p>
                                                        <p>({item?.discount || ''})</p>
                                                    </>
                                                )}

                                                {showButton && (
                                                    <CommonButton
                                                        onClick={() => handleSubscribe(item?.id)}
                                                        title={buttonTitle}
                                                        className={`btn-style ${buttonClass}`}
                                                    />
                                                )}

                                                {!loginToken && (
                                                    <p>
                                                        No Trial Needed?{" "}
                                                        <Link
                                                            href="/signup"
                                                            onClick={() => {
                                                                sessionStorage.setItem("pricing", planName.toLowerCase());
                                                                sessionStorage.setItem("trial", "false");
                                                            }}
                                                            className="text-blue-500"
                                                        >
                                                            Pay Now
                                                        </Link>
                                                    </p>
                                                )}
                                            </div>

                                            <ul>
                                                {item?.rules?.map((rule, i) => (
                                                    <li className="d-flex gap-2 align-items-center" key={i}>
                                                        {rule.is_allowed ? (
                                                            <CheckIcon width="20" height="20" />
                                                        ) : (
                                                            <RedCrossIcon />
                                                        )}
                                                        <span>{rule.description}</span>
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>
                                    </Col>
                                );
                            })}

                        </Row>
                    </Container>
                </section>

                <div className="py-50">
                    <Container>
                        <FaqCard isPricing={true} />
                    </Container>
                </div>
            </div>
        </HomeLayout>
    );
};

export default PricingClient