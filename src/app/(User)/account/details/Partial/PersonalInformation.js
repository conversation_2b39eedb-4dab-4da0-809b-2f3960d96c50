'use client';
import React, { useState, useEffect } from 'react';
import { Col, Row } from 'react-bootstrap';
import { useSelector, useDispatch } from 'react-redux';
import { EditIconSvg, PlusIconSvg, WhiteDownArrow, SearchIcons, CheckIcon, RedCircleCrossIcon, GreyCheckIcon } from '@/assets/svgIcons/SvgIcon';
import TextInput from '@/Components/UI/TextInput';
import countries from 'world-countries';
import CustomDropdown from '@/Components/common/CustumDropdown';
import { get, put } from '@/utils/apiUtils';
import { setUser } from '@/redux/authSlice';

export default function PersonalInformation() {
    const [isEditing, setIsEditing] = useState(false);
    const [userData, setUserData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState(null);

    // Temporary state for editing
    const [tempFirstName, setTempFirstName] = useState('');
    const [tempLastName, setTempLastName] = useState('');
    const [selectedCountry, setSelectedCountry] = useState('');

    const dispatch = useDispatch();
    const reduxUser = useSelector((state) => state?.auth?.user || null);

    // Fetch user data from API
    const fetchUserData = async () => {
        try {
            setLoading(true);
            setError(null);

            const response = await get('/account');

            if (response.success && response.data) {
                setUserData(response.data);
                // Update Redux store with fresh user data
                dispatch(setUser(response.data));
                // Also update localStorage to ensure consistency
                localStorage.setItem('user', JSON.stringify(response.data));
            } else {
                throw new Error(response.message || 'Failed to fetch user data');
            }
        } catch (err) {
            console.error('Error fetching user data:', err);
            setError(err.message || 'Failed to load user information');

            // Fallback to Redux user data if API fails
            if (reduxUser) {
                setUserData(reduxUser);
            }
        } finally {
            setLoading(false);
        }
    };

    // Load user data on component mount
    useEffect(() => {
        fetchUserData();
    }, []);

    // Update temp values when userData changes
    useEffect(() => {
        if (userData) {
            setTempFirstName(userData.first_name || '');
            setTempLastName(userData.last_name || '');
            setSelectedCountry(userData.country || '');
        }
    }, [userData]);

    const selectCountry = (country) => {
        console.log('Selected:', country);
        setSelectedCountry(country.name.common);
    };

    const handleEdit = () => {
        if (userData) {
            setTempFirstName(userData.first_name || '');
            setTempLastName(userData.last_name || '');
            setSelectedCountry(userData.country || '');
        }
        setIsEditing(true);
    };

    const handleSave = async () => {
        if (!userData) {
            toast.error('User data not available');
            return;
        }

        try {
            setSaving(true);
            setError(null);

            const updateData = {
                first_name: tempFirstName.trim(),
                last_name: tempLastName.trim(),
                country: selectedCountry
            };

            const response = await put(`/account/update/${userData.id}`, updateData);

            if (response.message) {
                toast.success(response.message);

                // Update local state with the fresh user data from response
                if (response.user) {
                    setUserData(response.user);
                    // Update Redux store
                    dispatch(setUser(response.user));
                    // Update localStorage
                    localStorage.setItem('user', JSON.stringify(response.user));
                }

                setIsEditing(false);
            } else {
                throw new Error('Failed to update user information');
            }
        } catch (err) {
            console.error('Error updating user data:', err);
            const errorMessage = err.response?.data?.message || err.message || 'Failed to update user information';
            toast.error(errorMessage);
            setError(errorMessage);
        } finally {
            setSaving(false);
        }
    };

    const handleCancel = () => {
        setIsEditing(false);
        if (userData) {
            setTempFirstName(userData.first_name || '');
            setTempLastName(userData.last_name || '');
            setSelectedCountry(userData.country || '');
        }
        setError(null);
    };

    const maskName = (name) => {
        if (!name || name.length === 0) return '';
        if (name.length === 1) return name;
        return name[0] + '*'.repeat(name.length - 1);
    };

    // Check if names are empty to show "Add Name" functionality
    const hasName = () => {
        return userData?.first_name || userData?.last_name;
    };

    // Show loading state
    if (loading) {
        return (
            <Col lg={12} xs={12} className="mb-4 mb-lg-4">
                <div className="common_blackcard account_card">
                    <div className="common_blackcard_innerheader">
                        <div className="common_blackcard_innerheader_content">
                            <h6>Personal Information</h6>
                        </div>
                    </div>
                    <div className="common_blackcard_innerbody">
                        <div className="account_card_list">
                            <p>Loading...</p>
                        </div>
                    </div>
                </div>
            </Col>
        );
    }

    // Show error state if no data and error exists
    if (!userData && error) {
        return (
            <Col lg={12} xs={12} className="mb-4 mb-lg-4">
                <div className="common_blackcard account_card">
                    <div className="common_blackcard_innerheader">
                        <div className="common_blackcard_innerheader_content">
                            <h6>Personal Information</h6>
                        </div>
                    </div>
                    <div className="common_blackcard_innerbody">
                        <div className="account_card_list">
                            <p style={{ color: 'red' }}>Error: {error}</p>
                            <button className="btn-style" onClick={fetchUserData}>
                                Retry
                            </button>
                        </div>
                    </div>
                </div>
            </Col>
        );
    }

    return (
        <Col lg={12} xs={12} className="mb-4 mb-lg-4">
            <div className="common_blackcard account_card">
                <div className="common_blackcard_innerheader">
                    <div className="common_blackcard_innerheader_content">
                        <h6>Personal Information</h6>
                    </div>
                    <div className="common_blackcard_innerheader_icon">
                        <button
                            className="d-flex align-items-center"
                            onClick={handleEdit}
                            disabled={saving || loading}
                        >
                            {hasName() ? <EditIconSvg /> : <PlusIconSvg />}
                            <span className="ms-2">Update</span>
                        </button>
                    </div>
                </div>
                <div className="common_blackcard_innerbody">
                    <div className="account_card_list">
                        <ul>
                            <li>
                                <Col xs={12} md={3}>
                                    <span className="label">Name </span>
                                </Col>
                                <Col xs={12} md={9}>
                                    {isEditing ? (
                                        <div className="account_card_list_form">
                                            <Row className="w-100">
                                                <Col xs={6}>
                                                    <TextInput
                                                        className="mb-0"
                                                        placeholder="First Name"
                                                        value={tempFirstName}
                                                        onChange={(e) => setTempFirstName(e.target.value)}
                                                    />
                                                </Col>
                                                <Col xs={6}>
                                                    <TextInput
                                                        className="mb-0"
                                                        placeholder="Last Name"
                                                        value={tempLastName}
                                                        onChange={(e) => setTempLastName(e.target.value)}
                                                    />
                                                </Col>
                                            </Row>
                                        </div>
                                    ) : (
                                        <span>
                                            {hasName()
                                                ? `${maskName(userData?.first_name)} ${maskName(userData?.last_name)}`.trim()
                                                : 'Not set'
                                            }
                                        </span>
                                    )}
                                </Col>
                            </li>

                            <li>
                                <Col xs={12} md={3}>
                                    <span >Country/Region </span>
                                </Col>
                                <Col xs={12} md={9}>
                                    {isEditing ? (
                                        <div className="account_card_list_form">
                                            <Row>
                                                <Col xs={12}>
                                                    <CustomDropdown
                                                        key={`country-${selectedCountry || 'none'}`}
                                                        options={countries.map((c) => ({ label: c.name.common, ...c }))}
                                                        defaultValue={selectedCountry || "Select Country"}
                                                        onSelect={selectCountry}
                                                    />
                                                </Col>
                                            </Row>
                                        </div>
                                    ) : (
                                        <span>{userData?.country || 'Not set'}</span>
                                    )}
                                </Col>
                            </li>
                        </ul>

                        {isEditing && (
                            <div className="account_card_list_btns mt-xxl-0 mt-3">
                                <button
                                    className="btn-style"
                                    onClick={handleSave}
                                    disabled={saving}
                                >
                                    {saving ? 'Saving...' : 'Save'}
                                </button>
                                <button
                                    className="btn-style gray-btn"
                                    onClick={handleCancel}
                                    disabled={saving}
                                >
                                    Cancel
                                </button>
                            </div>
                        )}

                        {error && !isEditing && (
                            <div className="mt-3">
                                <p style={{ color: 'red', fontSize: '14px' }}>
                                    {error}
                                </p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </Col>
    );
}
