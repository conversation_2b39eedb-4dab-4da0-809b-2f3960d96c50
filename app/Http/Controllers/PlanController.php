<?php

namespace App\Http\Controllers;

use App\Models\Plan;
use Stripe\Stripe;
use Stripe\Customer;
use App\Models\User;
use App\Models\UserSubscription;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Stripe\Checkout\Session;
use Illuminate\Support\Facades\Log;
use Stripe\Webhook;

class PlanController extends Controller
{
    public function index()
    {
        $plans = Plan::with('rules')
            ->where('billing_type', '!=', 'free')
            ->get();

        return response()->json([
            'status' => 'success',
            'data' => $plans,
        ]);
    }

    public function createCheckoutSession(Request $request)
    {
        Stripe::setApiKey(config('services.stripe.secret'));

        $request->validate([
           'plan_id' => 'required|exists:plans,id'
        ]);

        $user = auth()->user();

        $plan = Plan::find($request->plan_id);

        if (!$plan || !$plan->stripe_price_id) {
            return response()->json(['error' => 'Invalid plan or missing Stripe price ID'], 400);
        }

        try {
            $customerId = $this->getOrCreateCustomer($user);
            $sessionData = [
                'payment_method_types' => ['card'],
                'mode'                 => 'subscription',
                'customer'             => $customerId,
                'line_items'           => [[
                    'price'    => $plan->stripe_price_id,
                    'quantity' => 1,
                ]],
                'success_url' => env('FRONTEND_URL', 'http://localhost:3000') . '/pricing?session_id={CHECKOUT_SESSION_ID}',
                'cancel_url'  => env('FRONTEND_URL', 'http://localhost:3000') . '/pricing',
            ];

            $session = Session::create($sessionData);

            return response()->json([
                'status' => 'success',
                'session_id' => $session->id,
            ], status: 200);
        } catch (\Stripe\Exception\ApiErrorException $e) {
            Log::error('Stripe checkout error: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to create checkout session: ' . $e->getMessage()], 500);
        } catch (\Exception $e) {
            Log::error('General error: ' . $e->getMessage());
            return response()->json(['error' => 'An unexpected error occurred'], 500);
        }
    }

    public function confirmSubscription(Request $request)
    {
        Stripe::setApiKey(config('services.stripe.secret'));

        $request->validate([
            'session_id' => 'required|string',
        ]);

        $user = auth()->user();

        $session = Session::retrieve([
            'id' => $request->session_id,
            'expand' => ['line_items.data.price.product']
        ]);

        $subscriptionId = $session->subscription;

        if (!$subscriptionId) {
            return response()->json(['error' => 'Subscription ID not found in session.'], 400);
        }

        $priceId = $session->line_items->data[0]->price->id;

        $plan = Plan::where('stripe_price_id', $priceId)->first();

        if (!$plan) {
            return response()->json(['error' => 'No matching plan found for this session.'], 404);
        }

        $stripeSubscription = \Stripe\Subscription::retrieve($subscriptionId);

        UserSubscription::where('user_id', $user->id)
                ->where('status', 'active')
                ->update(['status' => 'deactive']);

        $userSubscription = UserSubscription::create([
            'user_id'               => $user->id,
            'plan_id'               => $plan->id,
            'stripe_subscription_id' => $stripeSubscription->id,
            'is_trial'              => false,
            'status'                => 'active',
            'starts_at'             => Carbon::createFromTimestamp($stripeSubscription->current_period_start),
            'ends_at'               => Carbon::createFromTimestamp($stripeSubscription->current_period_end),
        ]);

        return response()->json([
            'status' => 'success',
            'data'   => $userSubscription
        ]);
    }

    public function handleWebhook(Request $request)
    {
        Stripe::setApiKey(config('services.stripe.secret'));

        $payload = $request->getContent();
        $sigHeader = $request->header('Stripe-Signature');

        try {
            $event = Webhook::constructEvent(
                $payload,
                $sigHeader,
                config('services.stripe.webhook_secret')
            );
        } catch (\Exception $e) {
            Log::error('Stripe webhook error: ' . $e->getMessage());
            return response()->json(['error' => 'Webhook verification failed.'], 400);
        }

        $eventType = $event->type;
        $data = $event->data->object;

        switch ($eventType) {
            case 'invoice.payment_succeeded':
                Log::info('Payment succeeded for invoice: ' . $data->id);
                break;

            case 'invoice.payment_failed':
                Log::warning('Payment failed for invoice: ' . $data->id);
                break;

            case 'customer.subscription.updated':
                $this->updateSubscription($data);
                break;

            case 'customer.subscription.deleted':
                $this->downgradeToFree($data);
                break;

            case 'invoice.upcoming':
                Log::info('Invoice upcoming for customer: ' . $data->customer);
                break;
        }

        return response()->json(['status' => 'success']);
    }

    protected function updateSubscription($data)
    {
        $user = User::where('stripe_customer_id', $data->customer)->first();
        if (!$user) {
            return;
        }

        $subscription = UserSubscription::where('stripe_subscription_id', $data->id)->first();
        if ($subscription) {
            $subscription->update([
                'ends_at' => Carbon::createFromTimestamp($data->current_period_end),
                'status' => $data->cancel_at_period_end ? 'cancelled' : 'active',
            ]);
        }
    }

    protected function downgradeToFree($data)
    {
        $user = User::where('stripe_customer_id', $data->customer)->first();
        if (!$user) {
            return;
        }

        UserSubscription::where('stripe_subscription_id', $data->id)
            ->update(['status' => 'deactive']);

        $freePlan = Plan::where('billing_type', 'free')->first();
        if (!$freePlan) {
            return;
        }

        UserSubscription::create([
            'user_id'     => $user->id,
            'plan_id'     => $freePlan->id,
            'status'      => 'active',
            'is_trial'    => false,
            'starts_at'   => now(),
            'ends_at'     => null,
        ]);
    }

    /**
     * @param User $user
     * @return string
     * @throws \Stripe\Exception\ApiErrorException
     */
    public function getOrCreateCustomer(User $user): string
    {
        if ($user->stripe_customer_id) {
            return $user->stripe_customer_id;
        }

        $customer = Customer::create([
            'email' => $user->email,
            'name'  => $user->name,
        ]);

        $user->stripe_customer_id = $customer->id;
        $user->save();

        return $customer->id;
    }
}
