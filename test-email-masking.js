// Test script to analyze email masking behavior for edge cases

// YourInformation.js implementation
const maskEmailYourInfo = (email) => {
    if (!email) return '';
    const [localPart, domain] = email.split('@');
    if (!localPart || !domain) return email;

    // Display first two characters as ** and keep the last character of the local part
    const maskedLocalPart = `**${localPart[localPart.length - 1]}`;

    return `${maskedLocalPart}@${domain}`;
};

// utils/emailMask.js implementation
const maskEmailUtils = (email) => {
    if (!email) return "";

    const parts = email.split("@");
    if (parts.length !== 2) return email;

    const username = parts[0];
    const domain = parts[1];

    if (username.length <= 2) {
        return username[0] + "*" + "@" + domain;
    }

    const firstChar = username[0];
    const lastChar = username[username.length - 1];
    const masked = "*".repeat(username.length - 2);

    return firstChar + masked + lastChar + "@" + domain;
};

// Test cases
const testEmails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
];

console.log('=== EMAIL MASKING ANALYSIS ===\n');

testEmails.forEach(email => {
    console.log(`Input: ${email}`);
    console.log(`YourInformation.js: ${maskEmailYourInfo(email)}`);
    console.log(`utils/emailMask.js: ${maskEmailUtils(email)}`);
    console.log('---');
});
